2025-08-21 18:25:25,379 - 3_rag_text_splitting.py - INFO - ==================================================
2025-08-21 18:25:25,379 - 3_rag_text_splitting.py - INFO - Starting RAG Text Splitting Application
2025-08-21 18:25:25,379 - 3_rag_text_splitting.py - INFO - ==================================================
2025-08-21 18:25:25,395 - 3_rag_text_splitting.py - INFO - Books Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books
2025-08-21 18:25:25,395 - 3_rag_text_splitting.py - INFO - Database Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\db
2025-08-21 18:25:25,395 - 3_rag_text_splitting.py - INFO - Database directory created/verified successfully
2025-08-21 18:25:26,462 - 3_rag_text_splitting.py - INFO - File Path: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books\romeo_and_juliet.txt
2025-08-21 18:25:26,469 - 3_rag_text_splitting.py - INFO - ---- Using character-based splitting ----
2025-08-21 18:25:26,469 - 3_rag_text_splitting.py - INFO - Vector store 'chroma_db_character' already exists. No need to initialize.
2025-08-21 18:25:26,469 - 3_rag_text_splitting.py - INFO - ---- Using sentence-based splitting ----
2025-08-21 18:25:37,140 - 3_rag_text_splitting.py - INFO - Vector store 'chroma_db_sentence' already exists. No need to initialize.
2025-08-21 18:25:37,140 - 3_rag_text_splitting.py - INFO - ---- Using token-based splitting ----
2025-08-21 18:25:37,441 - 3_rag_text_splitting.py - INFO - Vector store 'chroma_db_token' already exists. No need to initialize.
2025-08-21 18:25:37,441 - 3_rag_text_splitting.py - INFO - ---- Using recursive character-based splitting ----
2025-08-21 18:25:37,441 - 3_rag_text_splitting.py - INFO - Vector store 'chroma_db_recursive_character' already exists. No need to initialize.
2025-08-21 18:25:37,441 - 3_rag_text_splitting.py - INFO - ---- Using custom-based splitting ----
2025-08-21 18:25:37,452 - 3_rag_text_splitting.py - INFO - Vector store 'chroma_db_custom' already exists. No need to initialize.
2025-08-21 18:25:37,452 - 3_rag_text_splitting.py - INFO - Querying vector store 'chroma_db_character'...
2025-08-21 18:25:38,262 - 3_rag_text_splitting.py - INFO - For vector store 'chroma_db_character', found 1 relevant documents
2025-08-21 18:25:38,262 - 3_rag_text_splitting.py - INFO - Querying vector store 'chroma_db_sentence'...
2025-08-21 18:25:38,495 - 3_rag_text_splitting.py - INFO - For vector store 'chroma_db_sentence', found 1 relevant documents
2025-08-21 18:25:38,495 - 3_rag_text_splitting.py - INFO - Querying vector store 'chroma_db_token'...
2025-08-21 18:25:38,680 - 3_rag_text_splitting.py - INFO - For vector store 'chroma_db_token', found 1 relevant documents
2025-08-21 18:25:38,680 - 3_rag_text_splitting.py - INFO - Querying vector store 'chroma_db_recursive_character'...
2025-08-21 18:25:38,886 - 3_rag_text_splitting.py - INFO - For vector store 'chroma_db_recursive_character', found 1 relevant documents
2025-08-21 18:25:38,886 - 3_rag_text_splitting.py - INFO - Querying vector store 'chroma_db_custom'...
2025-08-21 18:25:39,081 - 3_rag_text_splitting.py - INFO - For vector store 'chroma_db_custom', found 1 relevant documents
2025-08-21 18:30:31,688 - 4_rag_multi_model_embeddings.py - INFO - ==================================================
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - Starting RAG Multi Model Embeddings Application
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - ==================================================
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - Books Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - Database Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\db
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - Database directory created/verified successfully
2025-08-21 18:30:31,690 - 4_rag_multi_model_embeddings.py - INFO - File Path: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books\odyssey.txt
2025-08-21 18:30:43,629 - 4_rag_multi_model_embeddings.py - INFO - Vector store 'chroma_db_openai' already exists. No need to initialize.
2025-08-21 18:30:43,629 - 4_rag_multi_model_embeddings.py - INFO - Vector store 'chroma_db_huggingface' already exists. No need to initialize.
2025-08-21 18:30:43,629 - 4_rag_multi_model_embeddings.py - INFO - Querying vector store 'chroma_db_openai'...
2025-08-21 18:30:45,461 - 4_rag_multi_model_embeddings.py - INFO - For vector store 'chroma_db_openai', found 3 relevant documents
2025-08-21 18:30:45,461 - 4_rag_multi_model_embeddings.py - INFO - Querying vector store 'chroma_db_huggingface'...
2025-08-21 18:30:45,767 - 4_rag_multi_model_embeddings.py - INFO - For vector store 'chroma_db_huggingface', found 3 relevant documents
2025-08-21 21:43:10,090 - 5_rag_retriever_search_types.py - INFO - ==================================================
2025-08-21 21:43:10,090 - 5_rag_retriever_search_types.py - INFO - Starting RAG Retriever Search Types Embeddings Application
2025-08-21 21:43:10,090 - 5_rag_retriever_search_types.py - INFO - ==================================================
2025-08-21 21:43:20,239 - 5_rag_retriever_search_types.py - INFO - Querying vector store 'chroma_db_with_metadata'...
2025-08-21 21:43:20,670 - 5_rag_retriever_search_types.py - INFO - Relevant documents retrieved using search type 'similarity' and search kwargs '{'k': 3}' from vector store 'chroma_db_with_metadata'
2025-08-21 21:43:20,685 - 5_rag_retriever_search_types.py - INFO - Querying vector store 'chroma_db_with_metadata'...
2025-08-21 21:43:20,775 - 5_rag_retriever_search_types.py - INFO - Relevant documents retrieved using search type 'mmr' and search kwargs '{'k': 3, 'fetch_k': 20, 'lambda_mult': 0.5}' from vector store 'chroma_db_with_metadata'
2025-08-21 21:43:20,777 - 5_rag_retriever_search_types.py - INFO - Querying vector store 'chroma_db_with_metadata'...
2025-08-21 21:43:20,892 - 5_rag_retriever_search_types.py - INFO - No relevant documents found
2025-08-23 16:39:31,743 - 6_rag_retriever_llm_response.py - INFO - ==================================================
2025-08-23 16:39:31,743 - 6_rag_retriever_llm_response.py - INFO - Starting RAG Retriever LLM Response Application
2025-08-23 16:39:31,743 - 6_rag_retriever_llm_response.py - INFO - ==================================================
2025-08-23 16:39:33,719 - 6_rag_retriever_llm_response.py - INFO - Querying vector store 'chroma_db_with_metadata'...
2025-08-23 16:39:34,628 - 6_rag_retriever_llm_response.py - INFO - Retrieved vector store 'chroma_db_with_metadata', found 3 relevant documents.
2025-08-23 16:39:34,628 - 6_rag_retriever_llm_response.py - INFO - Generating LLMs response on retrieved relevant documents...
2025-08-23 16:39:57,697 - 6_rag_retriever_llm_response.py - INFO - AI response generated successfully
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - ==================================================
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - Starting Create RAG With Metadata Application
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - ==================================================
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - Books Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - Persistent Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\db\chroma_db_with_metadata
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - Vector store already exists. No need to initialize.
2025-08-24 00:57:28,462 - rag_with_metadata.py - INFO - Using existing vector store - no initialization needed
2025-08-24 01:00:38,908 - rag_with_metadata.py - INFO - ==================================================
2025-08-24 01:00:38,908 - rag_with_metadata.py - INFO - Starting Create RAG With Metadata Application
2025-08-24 01:00:38,908 - rag_with_metadata.py - INFO - ==================================================
2025-08-24 01:00:38,922 - rag_with_metadata.py - INFO - Books Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\books
2025-08-24 01:00:38,922 - rag_with_metadata.py - INFO - Persistent Directory: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\db\chroma_db_with_metadata
2025-08-24 01:00:38,938 - rag_with_metadata.py - INFO - Testing document loading...
2025-08-24 01:00:38,938 - rag_with_metadata.py - INFO - Successfully loaded: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\tests\test_books\empty_book.txt
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Successfully loaded: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\tests\test_books\test_book.txt
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Document loading test completed successfully
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Testing text chunking...
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Successfully created 5 document chunks
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Text chunking test completed successfully
2025-08-24 01:00:38,954 - rag_with_metadata.py - INFO - Testing vector store initialization for a new store (with mocks)...
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Initializing new vector store...
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Successfully loaded: C:\Users\<USER>\LangChain_Tutorial\langchain-crash-course\4_rag\tests\test_books\test_book.txt
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Successfully created 1 document chunks
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Creating embeddings...
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Embeddings created successfully
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Initializing Chroma vector store...
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Vector store initialized successfully
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Vector store initialization test (new store, mocked) completed successfully
2025-08-24 01:00:38,972 - rag_with_metadata.py - INFO - Testing vector store initialization with existing store...
2025-08-24 01:00:39,002 - rag_with_metadata.py - INFO - Vector store already exists. No need to initialize.
2025-08-24 01:00:39,004 - rag_with_metadata.py - INFO - Vector store initialization test (existing store) completed successfully
2025-08-24 01:00:39,014 - rag_with_metadata.py - INFO - Testing main flow with: init_return=new_store, dir_exists=False
2025-08-24 01:00:39,014 - rag_with_metadata.py - INFO - Vector store initialization completed successfully
2025-08-24 01:00:39,024 - rag_with_metadata.py - INFO - Testing main flow with: init_return=None, dir_exists=True
2025-08-24 01:00:39,025 - rag_with_metadata.py - INFO - Using existing vector store - no initialization needed
2025-08-24 01:00:39,031 - rag_with_metadata.py - INFO - Testing main flow with: init_return=None, dir_exists=False
2025-08-24 01:00:39,032 - rag_with_metadata.py - ERROR - Vector store initialization failed - system may not function correctly
2025-08-24 23:42:18,800 - 7_rag_llm_conversation.py - INFO - ==================================================
2025-08-24 23:42:18,800 - 7_rag_llm_conversation.py - INFO - Starting RAG LLM Conversation Application
2025-08-24 23:42:18,816 - 7_rag_llm_conversation.py - INFO - ==================================================
2025-08-24 23:42:20,885 - 7_rag_llm_conversation.py - INFO - Loading vector store 'chroma_db_with_metadata'...
2025-08-24 23:42:21,043 - 7_rag_llm_conversation.py - INFO - Created retriever from vector store 'chroma_db_with_metadata' successfully.
2025-08-24 23:42:44,598 - 7_rag_llm_conversation.py - INFO - Processing user query through RAG chain...
2025-08-24 23:43:05,085 - 7_rag_llm_conversation.py - INFO - AI response generated successfully
2025-08-24 23:43:05,085 - 7_rag_llm_conversation.py - INFO - Chat history updated successfully
2025-08-24 23:43:23,721 - 7_rag_llm_conversation.py - INFO - Processing user query through RAG chain...
2025-08-24 23:44:33,599 - 7_rag_llm_conversation.py - INFO - AI response generated successfully
2025-08-24 23:44:33,599 - 7_rag_llm_conversation.py - INFO - Chat history updated successfully
2025-08-24 23:45:17,573 - 7_rag_llm_conversation.py - INFO - User exited conversation
2025-08-25 00:07:42,148 - 7_rag_llm_conversation.py - INFO - ==================================================
2025-08-25 00:07:42,148 - 7_rag_llm_conversation.py - INFO - Starting RAG LLM Conversation Application
2025-08-25 00:07:42,148 - 7_rag_llm_conversation.py - INFO - ==================================================
2025-08-25 00:07:44,236 - 7_rag_llm_conversation.py - INFO - Loading vector store 'chroma_db_with_metadata'...
2025-08-25 00:07:44,386 - 7_rag_llm_conversation.py - INFO - Created retriever from vector store 'chroma_db_with_metadata' successfully.
2025-08-25 00:08:19,720 - 7_rag_llm_conversation.py - INFO - Processing user query through RAG chain...
2025-08-25 00:08:20,442 - 7_rag_llm_conversation.py - ERROR - Unexpected error: model "gemma3:4" not found, try pulling it first (status code: 404)
2025-08-26 00:47:51,349 - 8_rag_web_scrape_basic.py - INFO - ==================================================
2025-08-26 00:47:51,349 - 8_rag_web_scrape_basic.py - INFO - Starting RAG Web Scraping Basic Application
2025-08-26 00:47:51,349 - 8_rag_web_scrape_basic.py - INFO - ==================================================
2025-08-26 00:47:52,335 - 8_rag_web_scrape_basic.py - INFO - Loading documents from ['https://www.apple.com/']...
2025-08-26 00:47:53,023 - 8_rag_web_scrape_basic.py - INFO - Loaded 1 documents from ['https://www.apple.com/'] successfully.
2025-08-26 00:47:53,031 - 8_rag_web_scrape_basic.py - INFO - Number of document chunks: 9
2025-08-26 00:47:53,041 - 8_rag_web_scrape_basic.py - INFO - Creating vector store 'chroma_db_web_scrape_basic'...
2025-08-26 00:47:55,916 - 8_rag_web_scrape_basic.py - INFO - Vector store 'chroma_db_web_scrape_basic' created successfully.
2025-08-26 00:49:10,150 - 8_rag_web_scrape_basic.py - INFO - Retrieving relevant documents...
2025-08-26 00:49:10,257 - 8_rag_web_scrape_basic.py - INFO - Relevant documents retrieved successfully
2025-08-26 00:50:15,136 - 8_rag_web_scrape_basic.py - INFO - Keyboard interrupt or EOF error
