[project]
name = "langchain-tutorial"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langchain-openai>=0.3.1",
    "langchain>=0.3.15",
    "langchain-community>=0.3.15",
    "python-dotenv>=1.0.1",
    "langchain-google-genai>=2.0.9",
    "langchain-anthropic>=0.3.3",
    "langchain-google-firestore>=0.5.0",
    "langchain-ollama>=0.2.3",
    "chromadb>=0.6.3",
    "langchain-chroma>=0.2.2",
    "streamlit>=1.43.2",
    "sentence-transformers>=5.1.0",
    "nltk>=3.9.1",
    "langchain-huggingface>=0.3.1",
    "beautifulsoup4>=4.13.5",
    "firecrawl-py>=3.3.2",
]

[dependency-groups]
dev = [
    "firebase-admin>=6.7.0",
    "pytest>=8.3.5",
    "ruff>=0.11.12",
]
