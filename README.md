# Langchain Tutorial

## langchain-crash-course

### 1_chat_models

- 1_chat_model_basic.py
- 2_chat_model_basic_conversation.py
- 3_chat_model_alternatives.py
- 4_chat_model_conversation_with_user.py

### 2_prompt_templates

- prompt_template.py

### 3_chains

- 1_chains_basic.py
- 2_chains_runnable_sequence.py
- 3_chains_extended.py
- 4_chains_parallel.py
- 5_chains_branching.py

### 4_rag

- 1a_rag_basic.py
- 1b_rag_basic_query.py
- 2a_rag_basic_metadata.py
- 2a_rag_basic_metadata_robust.py
- 2b_rag_basic_metadata.py
- 2b_rag_basic_metadata_robust.py
- 3_rag_text_splitting.py
- 4_rag_multi_model_embeddings.py
- 5_rag_retriever_search_types.py
- 6_rag_question_answering.py
- 7_rag_llm_conversation.py
- 8_rag_web_scrape_basic.py
- rag_with_metadata.py

### Ollama Models

- llama3.2:3b Simple, quick tasks

- gemma3:4b Balanced performance

- deepseek-r1:14b Complex analysis and best quality
